"use client";

import React, { useState, useCallback, useRef } from 'react';
import { Document, pdfjs } from 'react-pdf';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, 
  Settings, 
  Maximize2, 
  Minimize2,
  AlertCircle,
  FileText
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

// Import our new components
import DocumentTabs from '../navigation/document-tabs';
import ErrorRecovery from './error-recovery';
import { DocumentLoading, ViewerLoadingOverlay } from './loading-states';
import useMultiDocumentState from './hooks/useMultiDocumentState';
import PDFViewer from './pdf-viewer';
import PDFUpload from './pdf-upload';
import type { DocumentInstance } from '@/lib/types/pdf';
import { EnhancedE<PERSON>r<PERSON><PERSON><PERSON> } from './enhanced-error-handler';

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

interface MultiDocumentPDFViewerProps {
  maxDocuments?: number;
  showUploadOnEmpty?: boolean;
  className?: string;
  onDocumentChange?: (document: DocumentInstance | null) => void;
}

export default function MultiDocumentPDFViewer({
  maxDocuments = 5,
  showUploadOnEmpty = true,
  className,
  onDocumentChange
}: MultiDocumentPDFViewerProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showUpload, setShowUpload] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Multi-document state management
  const {
    documents,
    activeDocument,
    activeDocumentId,
    loadingState,
    documentErrors,
    openDocument,
    closeDocument,
    switchToDocument,
    reloadDocument,
    updateDocumentState,
    isDocumentLoading,
    getDocumentProgress,
    getDocumentStage,
    hasDocumentError,
    getDocumentError
  } = useMultiDocumentState({
    maxDocuments,
    autoSave: true,
    persistState: true,
    onDocumentChange,
    onError: (error, documentId) => {
      console.error('Document error:', error, documentId);
    }
  });

  // Handle new file selection
  const handleFileSelect = useCallback(async (file: string | File) => {
    try {
      setShowUpload(false);
      const documentId = await openDocument(file);
      toast.success('Document opened successfully');
    } catch (error) {
      EnhancedErrorHandler.categorizeAndHandle(
        error as Error,
        'file selection',
        { showToast: true }
      );
    }
  }, [openDocument]);

  // Handle document close with confirmation for unsaved changes
  const handleDocumentClose = useCallback((documentId: string) => {
    const document = documents.find(d => d.id === documentId);
    if (!document) return;

    // TODO: Check for unsaved changes and show confirmation dialog
    closeDocument(documentId);
  }, [documents, closeDocument]);

  // Handle error recovery actions
  const handleErrorRecovery = useCallback((
    documentId: string,
    action: 'retry' | 'file' | 'url' | 'close'
  ) => {
    switch (action) {
      case 'retry':
        reloadDocument(documentId);
        break;
      case 'file':
        // Open file dialog
        setShowUpload(true);
        break;
      case 'url':
        // Open URL dialog (could be implemented in PDFUpload)
        setShowUpload(true);
        break;
      case 'close':
        closeDocument(documentId);
        break;
    }
  }, [reloadDocument, closeDocument]);

  // Toggle fullscreen mode
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Handle fullscreen change events
  React.useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Show upload interface if no documents and showUploadOnEmpty is true
  if (documents.length === 0 && showUploadOnEmpty && !showUpload) {
    return (
      <div className={cn("h-screen flex flex-col", className)}>
        <PDFUpload onFileSelect={handleFileSelect} />
      </div>
    );
  }

  // Show upload dialog
  if (showUpload) {
    return (
      <div className={cn("h-screen flex flex-col", className)}>
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">Open New Document</h2>
          <Button
            variant="ghost"
            onClick={() => setShowUpload(false)}
            disabled={documents.length === 0}
          >
            Cancel
          </Button>
        </div>
        <div className="flex-1">
          <PDFUpload onFileSelect={handleFileSelect} />
        </div>
      </div>
    );
  }

  const currentDocument = activeDocument;
  const isCurrentDocumentLoading = activeDocumentId ? isDocumentLoading(activeDocumentId) : false;
  const currentDocumentError = activeDocumentId ? getDocumentError(activeDocumentId) : undefined;

  return (
    <div 
      ref={containerRef}
      className={cn("h-screen flex flex-col bg-background", className)}
    >
      {/* Header with document tabs */}
      <div className="border-b">
        <div className="flex items-center justify-between p-2">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-muted-foreground" />
            <span className="font-semibold">PDF Viewer</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowUpload(true)}
              title="Open new document"
            >
              <Plus className="h-4 w-4" />
            </Button>
            
            <Separator orientation="vertical" className="h-6" />
            
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFullscreen}
              title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Document tabs */}
        <DocumentTabs
          documents={documents}
          activeDocumentId={activeDocumentId}
          onDocumentSelect={switchToDocument}
          onDocumentClose={handleDocumentClose}
          onNewDocument={() => setShowUpload(true)}
          maxVisibleTabs={6}
        />
      </div>

      {/* Main content area */}
      <div className="flex-1 relative">
        {currentDocument ? (
          <>
            {/* Loading overlay */}
            {isCurrentDocumentLoading && (
              <ViewerLoadingOverlay
                message={`Loading ${currentDocument.title}...`}
                progress={activeDocumentId ? getDocumentProgress(activeDocumentId) : 0}
                onCancel={() => closeDocument(activeDocumentId!)}
              />
            )}

            {/* Error state */}
            {currentDocumentError && !isCurrentDocumentLoading && (
              <div className="absolute inset-0 flex items-center justify-center p-4">
                <ErrorRecovery
                  error={{
                    type: 'LOAD_FAILED' as any,
                    message: currentDocumentError.message,
                    canRetry: true,
                    suggestedAction: 'Try reloading the document'
                  }}
                  documentTitle={currentDocument.title}
                  onRetry={() => reloadDocument(activeDocumentId!)}
                  onSelectNewFile={(file) => {
                    closeDocument(activeDocumentId!);
                    handleFileSelect(file);
                  }}
                  onSelectNewUrl={(url) => {
                    closeDocument(activeDocumentId!);
                    handleFileSelect(url);
                  }}
                  onCancel={() => closeDocument(activeDocumentId!)}
                />
              </div>
            )}

            {/* PDF Viewer */}
            {!currentDocumentError && !isCurrentDocumentLoading && (
              <PDFViewer
                file={currentDocument.file}
                onClose={() => closeDocument(activeDocumentId!)}
              />
            )}
          </>
        ) : (
          /* No active document */
          <div className="flex items-center justify-center h-full">
            <Card className="p-8 text-center max-w-md">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Document Selected</h3>
              <p className="text-muted-foreground mb-4">
                Select a document from the tabs above or open a new one.
              </p>
              <Button onClick={() => setShowUpload(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Open Document
              </Button>
            </Card>
          </div>
        )}
      </div>

      {/* Status bar */}
      {documents.length > 0 && (
        <div className="border-t px-4 py-2 bg-muted/30">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>{documents.length} document{documents.length !== 1 ? 's' : ''} open</span>
              {loadingState.isLoading && (
                <span className="flex items-center gap-1">
                  <div className="animate-spin rounded-full h-3 w-3 border-b border-current"></div>
                  Loading...
                </span>
              )}
            </div>
            
            {currentDocument && (
              <div className="flex items-center gap-2">
                <span>Page {currentDocument.pageNumber} of {currentDocument.numPages}</span>
                <Separator orientation="vertical" className="h-3" />
                <span>{Math.round(currentDocument.scale * 100)}%</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
